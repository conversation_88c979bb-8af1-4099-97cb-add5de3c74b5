#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理节点获取脚本（多线程版本）
功能：从指定API获取代理节点并保存为YAML格式
"""

import requests
import time
import threading
from datetime import datetime
from typing import Optional, <PERSON><PERSON>
from concurrent.futures import Thread<PERSON>oolExecutor
from threading import Lock


class ProxyFetcher:
    def __init__(self, api_url: str):
        """
        初始化代理获取器

        Args:
            api_url: API地址
        """
        self.api_url = api_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.print_lock = Lock()  # 用于线程安全的打印

    def safe_print(self, message: str):
        """线程安全的打印函数"""
        with self.print_lock:
            print(message)

    def fetch_proxy_data(self, task_id: int) -> Tuple[int, Optional[str]]:
        """
        从API获取代理数据

        Args:
            task_id: 任务ID

        Returns:
            (任务ID, 获取到的原始数据)，如果失败返回(任务ID, None)
        """
        try:
            # 添加随机延迟，避免所有请求同时发起
            import random
            delay = random.uniform(0, 2)  # 0-2秒随机延迟
            time.sleep(delay)

            self.safe_print(f"线程 {threading.current_thread().name} - 第 {task_id} 次获取中...")
            response = self.session.get(self.api_url, timeout=30)
            response.raise_for_status()

            self.safe_print(f"✓ 第 {task_id} 次获取成功 - 状态码: {response.status_code}, 大小: {len(response.text)} 字符")

            return task_id, response.text

        except requests.exceptions.RequestException as e:
            self.safe_print(f"✗ 第 {task_id} 次获取失败: {e}")
            return task_id, None

    def save_raw_yaml(self, data: str, filename: str, task_id: int) -> bool:
        """
        直接保存原始YAML数据

        Args:
            data: 原始数据
            filename: 保存的文件名
            task_id: 任务ID

        Returns:
            是否保存成功
        """
        try:
            # 直接保存原始数据，保持API返回的格式
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(data)

            self.safe_print(f"✓ 第 {task_id} 次数据已保存到: {filename}")
            return True

        except Exception as e:
            self.safe_print(f"✗ 第 {task_id} 次保存文件失败: {e}")
            return False

    def run_fetch_cycle(self, fetch_count: int, thread_count: int = 3, interval: int = 1) -> None:
        """
        执行多线程获取循环

        Args:
            fetch_count: 获取次数
            thread_count: 线程数
            interval: 每次获取间隔时间（秒）
        """
        print(f"开始获取代理节点，总共 {fetch_count} 次")
        print(f"使用 {thread_count} 个线程并发获取")
        print(f"每次间隔 {interval} 秒")
        print("-" * 50)

        success_count = 0
        start_time = time.time()

        # 使用线程池执行器
        with ThreadPoolExecutor(max_workers=thread_count, thread_name_prefix="ProxyFetcher") as executor:
            # 快速提交所有任务，实现真正的并发
            futures = []
            for i in range(1, fetch_count + 1):
                future = executor.submit(self.fetch_proxy_data, i)
                futures.append((future, i))

            # 处理完成的任务
            for future, task_id in futures:
                try:
                    returned_task_id, data = future.result()

                    if data:
                        # 生成文件名（带序号）
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        # 添加毫秒确保文件名唯一
                        microsecond = datetime.now().microsecond // 1000
                        filename = f"节点_{returned_task_id:03d}_{timestamp}_{microsecond:03d}.yaml"

                        # 保存数据
                        if self.save_raw_yaml(data, filename, returned_task_id):
                            success_count += 1

                except Exception as e:
                    self.safe_print(f"✗ 第 {task_id} 次处理异常: {e}")

        end_time = time.time()
        total_time = end_time - start_time

        print("\n" + "=" * 50)
        print(f"获取完成！成功: {success_count}/{fetch_count}")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"平均每次: {total_time/fetch_count:.2f} 秒")


def main():
    """主函数"""
    # API地址
    api_url = "https://ohayoo-distribute.hf.space/api/v1/subscribe?token=335wsxrnx0ig4pg6&target=clash&list=1"

    # 创建获取器实例
    fetcher = ProxyFetcher(api_url)

    print("代理节点获取脚本（多线程版本）")
    print("=" * 50)

    try:
        # 获取用户输入
        fetch_count = int(input("请输入获取次数 (默认: 3): ") or "3")
        thread_count = int(input("请输入线程数 (默认: 3): ") or "3")
        interval = int(input("请输入每次间隔时间/秒 (默认: 1): ") or "1")

        if fetch_count <= 0:
            print("获取次数必须大于0")
            return

        if thread_count <= 0:
            print("线程数必须大于0")
            return

        if thread_count > fetch_count:
            thread_count = fetch_count
            print(f"线程数已调整为获取次数: {thread_count}")

        if interval < 0:
            print("间隔时间不能为负数")
            return

        # 确认执行
        print(f"\n配置确认:")
        print(f"API地址: {api_url}")
        print(f"获取次数: {fetch_count}")
        print(f"线程数: {thread_count}")
        print(f"间隔时间: {interval} 秒")

        confirm = input("\n是否开始执行? (y/N): ").lower()
        if confirm not in ['y', 'yes']:
            print("已取消执行")
            return

        # 执行获取
        fetcher.run_fetch_cycle(fetch_count, thread_count, interval)

    except KeyboardInterrupt:
        print("\n\n用户中断执行")
    except ValueError:
        print("输入格式错误，请输入数字")
    except Exception as e:
        print(f"执行出错: {e}")


if __name__ == "__main__":
    main()
