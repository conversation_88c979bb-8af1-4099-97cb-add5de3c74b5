# YAML代理节点合并去重工具

## 功能特点

✨ **主要功能**
- 🔄 YAML文件合并去重
- 🏷️ 智能节点重命名（支持30+国家/地区）
- ⚙️ 字段标准化处理
- 🚀 多线程处理，性能优化
- 🎨 Windows 11 Fluent Design界面

## 使用方法

### 1. 选择文件
- **选择YAML文件**: 手动选择一个或多个YAML文件
- **选择目录**: 自动搜索目录中的所有YAML文件

### 2. 配置选项
- **启用去重**: 移除重复的代理节点
- **启用重命名**: 根据地区信息标准化节点名称
- **启用字段标准化**: 统一字段命名格式
- **使用简化去重**: 更快的去重算法（精度稍低）

### 3. 设置输出
- 选择输出目录（可选，默认为源文件目录）

### 4. 开始处理
- 点击"开始处理"按钮
- 查看实时处理日志
- 等待处理完成

## 支持的代理协议

- Shadowsocks (SS)
- VMess
- Trojan
- VLESS
- Hysteria2
- ShadowsocksR (SSR)

## 支持的地区重命名

🌍 支持30+个国家和地区的智能识别和重命名：
- 🇭🇰 香港、🇹🇼 台湾、🇺🇸 美国、🇸🇬 新加坡
- 🇯🇵 日本、🇬🇧 英国、🇨🇦 加拿大、🇦🇺 澳大利亚
- 🇩🇪 德国、🇫🇷 法国、🇰🇷 韩国、🇲🇾 马来西亚
- 🇹🇭 泰国、🇻🇳 越南、🇮🇳 印度、🇧🇷 巴西
- 以及更多...

## 技术特性

- **高性能**: 多线程处理，支持大文件
- **内存优化**: 流式处理，降低内存占用
- **错误处理**: 完善的异常处理机制
- **用户友好**: 直观的GUI界面，实时日志显示

## 系统要求

- Windows 10/11 (推荐)
- 内存: 512MB 以上
- 磁盘空间: 100MB 以上

## 版本信息

- 版本: 2.0
- 构建日期: 2025-05-25
- 基于: Python + CustomTkinter + PyYAML

---

💡 **提示**: 如遇到问题，请查看日志输出获取详细信息。
