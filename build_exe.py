#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YAML代理节点合并去重工具 - 打包脚本
使用PyInstaller将GUI应用打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_dependencies():
    """检查必要的依赖"""
    # 包名映射：pip包名 -> import名
    required_packages = {
        'PyYAML': 'yaml',
        'customtkinter': 'customtkinter',
        'Pillow': 'PIL',
        'pyinstaller': 'PyInstaller'
    }

    missing_packages = []

    for pip_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {pip_name} 已安装")
        except ImportError:
            missing_packages.append(pip_name)
            print(f"❌ {pip_name} 未安装")

    if missing_packages:
        print(f"\n⚠️ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print(f"\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    return True


def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']

    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)

    # 清理spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        print(f"🧹 清理文件: {spec_file}")
        os.remove(spec_file)


def create_icon():
    """创建应用图标（如果不存在）"""
    icon_path = "app_icon.ico"

    if not os.path.exists(icon_path):
        print(f"⚠️ 未找到图标文件 {icon_path}，将使用默认图标")
        return None

    return icon_path


def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")

    # 获取图标路径
    icon_path = create_icon()

    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--windowed',                   # 无控制台窗口
        '--name=YAML代理节点合并去重工具',  # 可执行文件名称
        '--distpath=dist',              # 输出目录
        '--workpath=build',             # 工作目录
        '--clean',                      # 清理临时文件
        '--noconfirm',                  # 不询问覆盖
    ]

    # 添加图标
    if icon_path:
        cmd.extend(['--icon', icon_path])

    # 添加隐藏导入（确保所有依赖都被包含）
    hidden_imports = [
        'yaml',
        'customtkinter',
        'PIL',
        'PIL._tkinter_finder',
        'tkinter',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'threading',
        'queue'
    ]

    for module in hidden_imports:
        cmd.extend(['--hidden-import', module])

    # 添加数据文件（如果有）
    # cmd.extend(['--add-data', 'data;data'])

    # 优化选项
    cmd.extend([
        '--optimize=2',                 # 优化字节码
        '--strip',                      # 去除调试信息
        '--noupx',                      # 不使用UPX压缩（避免兼容性问题）
    ])

    # 主程序文件
    cmd.append('gui_main.py')

    print(f"📦 执行命令: {' '.join(cmd)}")

    try:
        # 执行PyInstaller
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功！")
        print(result.stdout)

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败！")
        print(f"错误代码: {e.returncode}")
        print(f"错误输出: {e.stderr}")
        return False


def optimize_exe():
    """优化生成的exe文件"""
    exe_path = Path("dist/YAML代理节点合并去重工具.exe")

    if not exe_path.exists():
        print("⚠️ 未找到生成的exe文件")
        return

    # 获取文件大小
    file_size = exe_path.stat().st_size
    file_size_mb = file_size / (1024 * 1024)

    print(f"📊 生成的exe文件大小: {file_size_mb:.2f} MB")

    # 如果文件过大，给出优化建议
    if file_size_mb > 100:
        print("💡 文件较大，建议优化:")
        print("  - 考虑使用 --exclude-module 排除不必要的模块")
        print("  - 使用 --onedir 模式而不是 --onefile")
        print("  - 检查是否包含了不必要的依赖")


def create_readme():
    """创建使用说明文件"""
    readme_content = """# YAML代理节点合并去重工具

## 功能特点

✨ **主要功能**
- 🔄 YAML文件合并去重
- 🏷️ 智能节点重命名（支持30+国家/地区）
- ⚙️ 字段标准化处理
- 🚀 多线程处理，性能优化
- 🎨 Windows 11 Fluent Design界面

## 使用方法

### 1. 选择文件
- **选择YAML文件**: 手动选择一个或多个YAML文件
- **选择目录**: 自动搜索目录中的所有YAML文件

### 2. 配置选项
- **启用去重**: 移除重复的代理节点
- **启用重命名**: 根据地区信息标准化节点名称
- **启用字段标准化**: 统一字段命名格式
- **使用简化去重**: 更快的去重算法（精度稍低）

### 3. 设置输出
- 选择输出目录（可选，默认为源文件目录）

### 4. 开始处理
- 点击"开始处理"按钮
- 查看实时处理日志
- 等待处理完成

## 支持的代理协议

- Shadowsocks (SS)
- VMess
- Trojan
- VLESS
- Hysteria2
- ShadowsocksR (SSR)

## 支持的地区重命名

🌍 支持30+个国家和地区的智能识别和重命名：
- 🇭🇰 香港、🇹🇼 台湾、🇺🇸 美国、🇸🇬 新加坡
- 🇯🇵 日本、🇬🇧 英国、🇨🇦 加拿大、🇦🇺 澳大利亚
- 🇩🇪 德国、🇫🇷 法国、🇰🇷 韩国、🇲🇾 马来西亚
- 🇹🇭 泰国、🇻🇳 越南、🇮🇳 印度、🇧🇷 巴西
- 以及更多...

## 技术特性

- **高性能**: 多线程处理，支持大文件
- **内存优化**: 流式处理，降低内存占用
- **错误处理**: 完善的异常处理机制
- **用户友好**: 直观的GUI界面，实时日志显示

## 系统要求

- Windows 10/11 (推荐)
- 内存: 512MB 以上
- 磁盘空间: 100MB 以上

## 版本信息

- 版本: 2.0
- 构建日期: {build_date}
- 基于: Python + CustomTkinter + PyYAML

---

💡 **提示**: 如遇到问题，请查看日志输出获取详细信息。
"""

    from datetime import datetime
    build_date = datetime.now().strftime("%Y-%m-%d")
    readme_content = readme_content.format(build_date=build_date)

    readme_path = "dist/使用说明.txt"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print(f"📝 已创建使用说明: {readme_path}")


def main():
    """主函数"""
    print("🚀 YAML代理节点合并去重工具 - 构建脚本")
    print("=" * 50)

    # 检查依赖
    print("\n1️⃣ 检查依赖...")
    if not check_dependencies():
        print("❌ 依赖检查失败，请安装缺少的包后重试")
        return False

    # 清理构建目录
    print("\n2️⃣ 清理构建目录...")
    clean_build_dirs()

    # 构建exe
    print("\n3️⃣ 构建exe文件...")
    if not build_exe():
        print("❌ 构建失败")
        return False

    # 优化检查
    print("\n4️⃣ 优化检查...")
    optimize_exe()

    # 创建说明文件
    print("\n5️⃣ 创建使用说明...")
    create_readme()

    print("\n🎉 构建完成！")
    print("📁 输出目录: dist/")
    print("📦 可执行文件: dist/YAML代理节点合并去重工具.exe")
    print("📝 使用说明: dist/使用说明.txt")

    return True


if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n构建失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出构建")
    except Exception as e:
        print(f"\n❌ 构建过程中发生错误: {e}")
        input("\n按回车键退出...")
