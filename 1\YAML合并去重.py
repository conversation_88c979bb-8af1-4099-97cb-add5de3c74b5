#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理节点去重和重命名工具

基于原项目的Go代码实现，提供以下功能：
1. 代理节点去重 - 根据服务器、端口、服务名和密码组合去重
2. 代理节点重命名 - 根据地区信息标准化节点名称
"""

import re
import threading
import base64
import json
import urllib.parse
import os
import glob
from typing import List, Dict, Any, Optional
from collections import defaultdict


class Base64Utils:
    """Base64编解码工具类"""

    @staticmethod
    def is_base64_string(s: str) -> bool:
        """检查字符串是否为有效的base64编码"""
        if not s or not isinstance(s, str):
            return False

        s = s.strip()
        if len(s) == 0:
            return False

        # 处理URL安全的base64
        s = s.replace("-", "+").replace("_", "/")

        # 添加缺失的填充
        if len(s) % 4 != 0:
            s += "=" * (4 - len(s) % 4)

        # 检查是否包含非base64字符
        valid_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
        s_without_padding = s.rstrip("=")
        for c in s_without_padding:
            if c not in valid_chars:
                return False

        # 尝试解码
        try:
            decoded = base64.b64decode(s)
            # 检查解码后的内容是否为有效的UTF-8字符串
            decoded.decode('utf-8')
            return True
        except Exception:
            return False

    @staticmethod
    def decode_base64(s: str) -> str:
        """解码base64字符串，如果不是base64则返回原始字符串"""
        if not Base64Utils.is_base64_string(s):
            return s

        try:
            # 处理URL安全的base64
            s = s.strip().replace("-", "+").replace("_", "/")

            # 添加缺失的填充
            if len(s) % 4 != 0:
                s += "=" * (4 - len(s) % 4)

            # 解码
            decoded = base64.b64decode(s)
            return decoded.decode('utf-8')
        except Exception:
            return s


class ProxyFieldNormalizer:
    """代理节点字段标准化器 - 基于Go代码的字段标准化逻辑"""

    @staticmethod
    def normalize_proxy_fields(proxy: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化代理节点字段名称
        将下划线格式转换为横杠格式，符合Clash规范
        """
        if not isinstance(proxy, dict):
            return proxy

        normalized = proxy.copy()

        # 根据代理类型进行特定的字段标准化
        proxy_type = normalized.get("type", "").lower()

        if proxy_type in ["hysteria2", "hy2"]:
            # Hysteria2特定字段标准化
            if "obfs_password" in normalized:
                normalized["obfs-password"] = normalized.pop("obfs_password")
            if "skip_cert_verify" in normalized:
                normalized["skip-cert-verify"] = normalized.pop("skip_cert_verify")

        # 通用字段标准化
        field_mappings = {
            "alter_id": "alterId",
            "alter-id": "alterId",
            "skip_cert_verify": "skip-cert-verify",
            "cert_verify": "skip-cert-verify",  # 反向映射
            "obfs_password": "obfs-password",
            "obfs_param": "obfs-param",
            "protocol_param": "protocol-param",
            "grpc_service_name": "grpc-service-name",
            "ws_path": "path",
            "ws_headers": "headers"
        }

        for old_field, new_field in field_mappings.items():
            if old_field in normalized:
                normalized[new_field] = normalized.pop(old_field)

        return normalized


class ProxyParser:
    """代理节点解析器 - 基于Go代码增强的解析功能"""

    @staticmethod
    def parse_proxy_url(proxy_url: str) -> Optional[Dict[str, Any]]:
        """解析代理URL为标准格式"""
        if not proxy_url or not isinstance(proxy_url, str):
            return None

        proxy_url = proxy_url.strip()

        try:
            if proxy_url.startswith("ss://"):
                return ProxyParser._parse_shadowsocks(proxy_url)
            elif proxy_url.startswith("vmess://"):
                return ProxyParser._parse_vmess(proxy_url)
            elif proxy_url.startswith("trojan://"):
                return ProxyParser._parse_trojan(proxy_url)
            elif proxy_url.startswith("vless://"):
                return ProxyParser._parse_vless(proxy_url)
            elif proxy_url.startswith(("hysteria2://", "hy2://")):
                return ProxyParser._parse_hysteria2(proxy_url)
            elif proxy_url.startswith("ssr://"):
                return ProxyParser._parse_ssr(proxy_url)
        except Exception as e:
            print(f"解析代理URL失败: {proxy_url[:50]}... 错误: {e}")

        return None

    @staticmethod
    def _parse_shadowsocks(url: str) -> Optional[Dict[str, Any]]:
        """解析Shadowsocks URL"""
        try:
            # 移除ss://前缀
            data = url[5:]

            # 检查是否包含@分隔符
            if "@" not in data:
                if "#" in data:
                    temp = data.split("#", 1)
                    data = Base64Utils.decode_base64(temp[0]) + "#" + temp[1]
                else:
                    data = Base64Utils.decode_base64(data)

            # 分离名称部分
            name = ""
            if "#" in data:
                data, name = data.rsplit("#", 1)
                name = urllib.parse.unquote(name)

            # 分离用户信息和服务器信息
            if "@" not in data:
                return None

            user_info, server_info = data.split("@", 1)
            user_info = Base64Utils.decode_base64(user_info)

            # 分离加密方式和密码
            if ":" not in user_info:
                return None

            method, password = user_info.split(":", 1)
            password = Base64Utils.decode_base64(password)

            # 分离服务器地址和端口
            if ":" not in server_info:
                return None

            server, port_str = server_info.rsplit(":", 1)
            port = int(port_str)

            return {
                "name": name,
                "type": "ss",
                "server": server,
                "port": port,
                "cipher": method,
                "password": password
            }
        except Exception:
            return None

    @staticmethod
    def _parse_vmess(url: str) -> Optional[Dict[str, Any]]:
        """解析VMess URL"""
        try:
            # 移除vmess://前缀
            data = url[8:].rstrip("`")

            # base64解码
            decoded = base64.b64decode(data).decode('utf-8')
            vmess_info = json.loads(decoded)

            # 处理端口
            port = vmess_info.get("port")
            if isinstance(port, str):
                port = int(port)

            # 处理alterId
            aid = vmess_info.get("aid", 0)
            if isinstance(aid, str):
                aid = int(aid)

            proxy = {
                "name": vmess_info.get("ps", ""),
                "type": "vmess",
                "server": vmess_info.get("add", ""),
                "port": port,
                "uuid": vmess_info.get("id", ""),
                "alterId": aid,
                "cipher": "auto",
                "network": vmess_info.get("net", "tcp"),
                "tls": vmess_info.get("tls") == "tls",
                "servername": vmess_info.get("sni", "")
            }

            # 添加传输层配置
            if vmess_info.get("net") == "ws":
                proxy["ws-opts"] = {
                    "path": vmess_info.get("path", "/"),
                    "headers": {"Host": vmess_info.get("host", "")} if vmess_info.get("host") else {}
                }

            return proxy
        except Exception:
            return None

    @staticmethod
    def _parse_trojan(url: str) -> Optional[Dict[str, Any]]:
        """解析Trojan URL - 基于Go代码增强的解析功能"""
        try:
            parsed = urllib.parse.urlparse(url)

            # 提取密码
            password = parsed.username or ""

            # 分离主机和端口
            server = parsed.hostname
            port = parsed.port

            if not server or not port:
                return None

            # 提取节点名称
            name = urllib.parse.unquote(parsed.fragment) if parsed.fragment else ""

            # 解析查询参数
            params = urllib.parse.parse_qs(parsed.query)

            # 基础配置
            proxy = {
                "name": name,
                "type": "trojan",
                "server": server,
                "port": port,
                "password": password,
                "network": params.get("type", ["original"])[0],
                "skip-cert-verify": params.get("allowInsecure", ["0"])[0] == "1",
                # 保留原始参数以便兼容
                "allowInsecure": params.get("allowInsecure", ["0"])[0]
            }

            # 添加TLS配置
            if params.get("security", [""])[0] == "tls":
                proxy["tls"] = True
                if "sni" in params:
                    proxy["sni"] = params["sni"][0]

            # 根据传输方式添加特定配置
            transport_type = params.get("type", [""])[0]
            if transport_type == "ws":
                # WebSocket配置
                ws_opts = {
                    "path": params.get("path", ["/"])[0]
                }
                if "host" in params:
                    ws_opts["headers"] = {"Host": params["host"][0]}
                proxy["ws-opts"] = ws_opts

            elif transport_type == "grpc":
                # gRPC配置
                if "serviceName" in params:
                    proxy["grpc-opts"] = {
                        "grpc-service-name": params["serviceName"][0]
                    }

            # 添加SNI（如果未在TLS配置中添加）
            if "sni" in params and "sni" not in proxy:
                proxy["sni"] = params["sni"][0]

            return ProxyFieldNormalizer.normalize_proxy_fields(proxy)
        except Exception:
            return None

    @staticmethod
    def _parse_vless(url: str) -> Optional[Dict[str, Any]]:
        """解析VLESS URL - 基于Go代码增强的解析功能"""
        try:
            parsed = urllib.parse.urlparse(url)

            if parsed.scheme != "vless":
                return None

            server = parsed.hostname
            port = parsed.port
            uuid = parsed.username

            if not server or not port or not uuid:
                return None

            # 解析查询参数
            params = urllib.parse.parse_qs(parsed.query)

            # 基础配置
            proxy = {
                "name": urllib.parse.unquote(parsed.fragment) if parsed.fragment else "",
                "type": "vless",
                "server": server,
                "port": port,
                "uuid": uuid,
                "network": params.get("type", ["tcp"])[0],
                "tls": params.get("security", ["none"])[0] != "none",
                "udp": params.get("udp", ["false"])[0].lower() == "true",
                "servername": params.get("sni", [""])[0],
                "flow": params.get("flow", [""])[0],
                "mode": params.get("mode", [""])[0],
                "client-fingerprint": params.get("fp", [""])[0]
            }

            # WebSocket配置
            path = params.get("path", [""])[0]
            host = params.get("host", [""])[0]
            if path or host:
                ws_opts = {"path": path}
                if host:
                    ws_opts["headers"] = {"Host": host}
                proxy["ws-opts"] = ws_opts

            # Reality配置
            pbk = params.get("pbk", [""])[0]
            sid = params.get("sid", [""])[0]
            if pbk or sid:
                proxy["reality-opts"] = {
                    "public-key": pbk,
                    "short-id": sid
                }

            # gRPC配置
            service_name = params.get("serviceName", [""])[0]
            if service_name:
                proxy["grpc-opts"] = {
                    "grpc-service-name": service_name
                }

            return ProxyFieldNormalizer.normalize_proxy_fields(proxy)
        except Exception:
            return None

    @staticmethod
    def _parse_hysteria2(url: str) -> Optional[Dict[str, Any]]:
        """解析Hysteria2 URL - 基于Go代码增强的解析功能"""
        try:
            parsed = urllib.parse.urlparse(url)

            server = parsed.hostname
            port = parsed.port
            password = parsed.username

            if not server or not port:
                return None

            # 解析查询参数
            params = urllib.parse.parse_qs(parsed.query)

            proxy = {
                "name": urllib.parse.unquote(parsed.fragment) if parsed.fragment else "",
                "type": "hysteria2",
                "server": server,
                "port": port,
                "ports": params.get("mport", [""])[0],  # 多端口支持
                "password": password or "",
                "obfs": params.get("obfs", [""])[0],
                "obfs-password": params.get("obfs-password", [""])[0],
                "sni": params.get("sni", [""])[0],
                "skip-cert-verify": params.get("insecure", ["0"])[0] == "1",
                # 保留原始参数以便兼容
                "insecure": params.get("insecure", ["0"])[0],
                "mport": params.get("mport", [""])[0]
            }

            return ProxyFieldNormalizer.normalize_proxy_fields(proxy)
        except Exception:
            return None

    @staticmethod
    def _parse_ssr(url: str) -> Optional[Dict[str, Any]]:
        """解析SSR URL"""
        try:
            # 移除ssr://前缀
            data = url[6:]
            data = Base64Utils.decode_base64(data)

            # 分离服务器信息和参数
            if "/?" in data:
                server_info, params_str = data.split("/?", 1)
            else:
                server_info = data
                params_str = ""

            # 解析服务器信息
            parts = server_info.split(":")
            if len(parts) < 6:
                return None

            server = parts[0]
            port = int(parts[1])
            protocol = parts[2]
            method = parts[3]
            obfs = parts[4]
            password = Base64Utils.decode_base64(parts[5])

            proxy = {
                "name": f"{server}:{port}",
                "type": "ssr",
                "server": server,
                "port": port,
                "password": password,
                "cipher": method,
                "obfs": obfs,
                "protocol": protocol
            }

            # 解析额外参数
            if params_str:
                params = urllib.parse.parse_qs(params_str)
                if "remarks" in params:
                    proxy["name"] = Base64Utils.decode_base64(params["remarks"][0])
                if "obfsparam" in params:
                    proxy["obfs-param"] = Base64Utils.decode_base64(params["obfsparam"][0])
                if "protoparam" in params:
                    proxy["protocol-param"] = Base64Utils.decode_base64(params["protoparam"][0])

            return proxy
        except Exception:
            return None


class ProxyCounter:
    """代理节点计数器，用于为不同地区的节点生成序号"""

    def __init__(self):
        self._counters = defaultdict(int)
        self._lock = threading.Lock()

    def get_next_count(self, region: str) -> int:
        """获取指定地区的下一个计数"""
        with self._lock:
            self._counters[region] += 1
            return self._counters[region]

    def reset(self):
        """重置所有计数器"""
        with self._lock:
            self._counters.clear()

    def get_count(self, region: str) -> int:
        """获取指定地区的当前计数"""
        return self._counters.get(region, 0)


class ProxyRenamer:
    """代理节点重命名器"""

    def __init__(self):
        self.counter = ProxyCounter()

        # 地区匹配规则 - 按照原Go代码的顺序和规则
        self.region_patterns = [
            # 香港
            {
                'patterns': [r'(?i)(hk|港|hongkong|hong kong)'],
                'name': '香港',
                'flag': '🇭🇰',
                'key': 'hk'
            },
            # 台湾
            {
                'patterns': [r'(?i)(tw|台|taiwan|tai wen)'],
                'name': '台湾',
                'flag': '🇹🇼',
                'key': 'tw'
            },
            # 美国
            {
                'patterns': [r'(?i)(us|美|united states|america)'],
                'name': '美国',
                'flag': '🇺🇸',
                'key': 'us'
            },
            # 新加坡
            {
                'patterns': [r'(?i)(sg|新|singapore|狮城)'],
                'name': '新加坡',
                'flag': '🇸🇬',
                'key': 'sg'
            },
            # 日本
            {
                'patterns': [r'(?i)(jp|日|japan)'],
                'name': '日本',
                'flag': '🇯🇵',
                'key': 'jp'
            },
            # 英国
            {
                'patterns': [r'(?i)(uk|英|united kingdom|britain|gb)'],
                'name': '英国',
                'flag': '🇬🇧',
                'key': 'uk'
            },
            # 加拿大
            {
                'patterns': [r'(?i)(ca|加|canada)'],
                'name': '加拿大',
                'flag': '🇨🇦',
                'key': 'ca'
            },
            # 澳大利亚
            {
                'patterns': [r'(?i)(au|澳|australia)'],
                'name': '澳大利亚',
                'flag': '🇦🇺',
                'key': 'au'
            },
            # 德国
            {
                'patterns': [r'(?i)(de|德|germany|deutschland)'],
                'name': '德国',
                'flag': '🇩🇪',
                'key': 'de'
            },
            # 法国
            {
                'patterns': [r'(?i)(fr|法|france)'],
                'name': '法国',
                'flag': '🇫🇷',
                'key': 'fr'
            },
            # 荷兰
            {
                'patterns': [r'(?i)(nl|荷|netherlands)'],
                'name': '荷兰',
                'flag': '🇳🇱',
                'key': 'nl'
            },
            # 俄罗斯
            {
                'patterns': [r'(?i)(ru|俄|russia)'],
                'name': '俄罗斯',
                'flag': '🇷🇺',
                'key': 'ru'
            },
            # 匈牙利
            {
                'patterns': [r'(?i)(hu|匈|hungary)'],
                'name': '匈牙利',
                'flag': '🇭🇺',
                'key': 'hu'
            },
            # 乌克兰
            {
                'patterns': [r'(?i)(ua|乌|ukraine)'],
                'name': '乌克兰',
                'flag': '🇺🇦',
                'key': 'ua'
            },
            # 波兰
            {
                'patterns': [r'(?i)(pl|波|poland)'],
                'name': '波兰',
                'flag': '🇵🇱',
                'key': 'pl'
            },
            # 韩国
            {
                'patterns': [r'(?i)(kr|韩|korea)'],
                'name': '韩国',
                'flag': '🇰🇷',
                'key': 'kr'
            },
            # 亚太地区
            {
                'patterns': [r'(?i)(ap|亚太|asia)'],
                'name': '亚太地区',
                'flag': '🌏',
                'key': 'ap'
            },
            # 伊朗
            {
                'patterns': [r'(?i)(ir|伊|iran)'],
                'name': '伊朗',
                'flag': '🇮🇷',
                'key': 'ir'
            },
            # 意大利
            {
                'patterns': [r'(?i)(it|意|italy)'],
                'name': '意大利',
                'flag': '🇮🇹',
                'key': 'it'
            },
            # 芬兰
            {
                'patterns': [r'(?i)(fi|芬|finland)'],
                'name': '芬兰',
                'flag': '🇫🇮',
                'key': 'fi'
            },
            # 柬埔寨
            {
                'patterns': [r'(?i)(kh|柬|cambodia)'],
                'name': '柬埔寨',
                'flag': '🇰🇭',
                'key': 'kh'
            },
            # 巴西
            {
                'patterns': [r'(?i)(br|巴|brazil)'],
                'name': '巴西',
                'flag': '🇧🇷',
                'key': 'br'
            },
            # 印度
            {
                'patterns': [r'(?i)(in|印|india)'],
                'name': '印度',
                'flag': '🇮🇳',
                'key': 'in'
            },
            # 阿拉伯酋长国
            {
                'patterns': [r'(?i)(ae|阿|uae|阿拉伯酋长国)'],
                'name': '阿拉伯酋长国',
                'flag': '🇦🇪',
                'key': 'ae'
            },
            # 瑞士
            {
                'patterns': [r'(?i)(ch|瑞|switzerland)'],
                'name': '瑞士',
                'flag': '🇨🇭',
                'key': 'ch'
            },
            # 匈牙利 - 基于Go代码补充
            {
                'patterns': [r'(?i)(hu|匈|hungary)'],
                'name': '匈牙利',
                'flag': '🇭🇺',
                'key': 'hu'
            },
            # 乌克兰 - 基于Go代码补充
            {
                'patterns': [r'(?i)(ua|乌|ukraine)'],
                'name': '乌克兰',
                'flag': '🇺🇦',
                'key': 'ua'
            },
            # 波兰 - 基于Go代码补充
            {
                'patterns': [r'(?i)(pl|波|poland)'],
                'name': '波兰',
                'flag': '🇵🇱',
                'key': 'pl'
            },
            # 柬埔寨 - 基于Go代码补充
            {
                'patterns': [r'(?i)(kh|柬|cambodia)'],
                'name': '柬埔寨',
                'flag': '🇰🇭',
                'key': 'kh'
            },
            # 马来西亚
            {
                'patterns': [r'(?i)(my|马来|malaysia)'],
                'name': '马来西亚',
                'flag': '🇲🇾',
                'key': 'my'
            },
            # 泰国
            {
                'patterns': [r'(?i)(th|泰|thailand)'],
                'name': '泰国',
                'flag': '🇹🇭',
                'key': 'th'
            },
            # 越南
            {
                'patterns': [r'(?i)(vn|越|vietnam)'],
                'name': '越南',
                'flag': '🇻🇳',
                'key': 'vn'
            },
            # 菲律宾
            {
                'patterns': [r'(?i)(ph|菲|philippines)'],
                'name': '菲律宾',
                'flag': '🇵🇭',
                'key': 'ph'
            },
            # 印度尼西亚
            {
                'patterns': [r'(?i)(id|印尼|indonesia)'],
                'name': '印度尼西亚',
                'flag': '🇮🇩',
                'key': 'id'
            },
            # 土耳其
            {
                'patterns': [r'(?i)(tr|土耳其|turkey)'],
                'name': '土耳其',
                'flag': '🇹🇷',
                'key': 'tr'
            },
            # 以色列
            {
                'patterns': [r'(?i)(il|以色列|israel)'],
                'name': '以色列',
                'flag': '🇮🇱',
                'key': 'il'
            },
            # 南非
            {
                'patterns': [r'(?i)(za|南非|south africa)'],
                'name': '南非',
                'flag': '🇿🇦',
                'key': 'za'
            },
            # 阿根廷
            {
                'patterns': [r'(?i)(ar|阿根廷|argentina)'],
                'name': '阿根廷',
                'flag': '🇦🇷',
                'key': 'ar'
            },
            # 智利
            {
                'patterns': [r'(?i)(cl|智利|chile)'],
                'name': '智利',
                'flag': '🇨🇱',
                'key': 'cl'
            },
            # 墨西哥
            {
                'patterns': [r'(?i)(mx|墨西哥|mexico)'],
                'name': '墨西哥',
                'flag': '🇲🇽',
                'key': 'mx'
            },
            # 挪威
            {
                'patterns': [r'(?i)(no|挪威|norway)'],
                'name': '挪威',
                'flag': '🇳🇴',
                'key': 'no'
            },
            # 瑞典
            {
                'patterns': [r'(?i)(se|瑞典|sweden)'],
                'name': '瑞典',
                'flag': '🇸🇪',
                'key': 'se'
            },
            # 丹麦
            {
                'patterns': [r'(?i)(dk|丹麦|denmark)'],
                'name': '丹麦',
                'flag': '🇩🇰',
                'key': 'dk'
            },
            # 比利时
            {
                'patterns': [r'(?i)(be|比利时|belgium)'],
                'name': '比利时',
                'flag': '🇧🇪',
                'key': 'be'
            },
            # 奥地利
            {
                'patterns': [r'(?i)(at|奥地利|austria)'],
                'name': '奥地利',
                'flag': '🇦🇹',
                'key': 'at'
            },
            # 捷克
            {
                'patterns': [r'(?i)(cz|捷克|czech)'],
                'name': '捷克',
                'flag': '🇨🇿',
                'key': 'cz'
            },
            # 罗马尼亚
            {
                'patterns': [r'(?i)(ro|罗马尼亚|romania)'],
                'name': '罗马尼亚',
                'flag': '🇷🇴',
                'key': 'ro'
            },
            # 保加利亚
            {
                'patterns': [r'(?i)(bg|保加利亚|bulgaria)'],
                'name': '保加利亚',
                'flag': '🇧🇬',
                'key': 'bg'
            },
            # 希腊
            {
                'patterns': [r'(?i)(gr|希腊|greece)'],
                'name': '希腊',
                'flag': '🇬🇷',
                'key': 'gr'
            },
            # 葡萄牙
            {
                'patterns': [r'(?i)(pt|葡萄牙|portugal)'],
                'name': '葡萄牙',
                'flag': '🇵🇹',
                'key': 'pt'
            },
            # 西班牙
            {
                'patterns': [r'(?i)(es|西班牙|spain)'],
                'name': '西班牙',
                'flag': '🇪🇸',
                'key': 'es'
            },
            # 爱尔兰
            {
                'patterns': [r'(?i)(ie|爱尔兰|ireland)'],
                'name': '爱尔兰',
                'flag': '🇮🇪',
                'key': 'ie'
            },
            # 新西兰
            {
                'patterns': [r'(?i)(nz|新西兰|new zealand)'],
                'name': '新西兰',
                'flag': '🇳🇿',
                'key': 'nz'
            }
        ]

    def rename(self, name: str) -> str:
        """
        根据节点名称重命名为标准格式

        Args:
            name: 原始节点名称

        Returns:
            重命名后的节点名称（序号为三位数格式，如001、002等）
        """
        if not name:
            count = self.counter.get_next_count('other')
            return f"🌀其他{count:03d}"

        # 按顺序匹配地区
        for region in self.region_patterns:
            for pattern in region['patterns']:
                if re.search(pattern, name):
                    count = self.counter.get_next_count(region['key'])
                    return f"{region['flag']}{region['name']}{count:03d}"

        # 如果没有匹配到任何地区，归类为其他（完全替换，不保留原名）
        count = self.counter.get_next_count('other')
        return f"🌀其他{count:03d}"

    def reset_counter(self):
        """重置计数器"""
        self.counter.reset()


class ProxyDeduplicator:
    """代理节点去重器 - 提供多种去重策略"""

    @staticmethod
    def _normalize_value(value: Any) -> str:
        """标准化值，用于生成一致的键"""
        if value is None:
            return ""
        if isinstance(value, bool):
            return str(value).lower()
        if isinstance(value, (int, float)):
            return str(value)
        return str(value).strip()

    @staticmethod
    def _get_simple_proxy_key(proxy: Dict[str, Any]) -> str:
        """
        生成简化的代理节点唯一标识键 - 基于Go代码的简化去重逻辑
        仅基于核心字段：server, port, servername, password/uuid
        """
        server = ProxyDeduplicator._normalize_value(proxy.get('server', ''))
        port = ProxyDeduplicator._normalize_value(proxy.get('port', ''))
        servername = ProxyDeduplicator._normalize_value(proxy.get('servername', ''))

        # 优先使用password，如果没有则使用uuid
        password = ProxyDeduplicator._normalize_value(proxy.get('password', ''))
        if not password:
            password = ProxyDeduplicator._normalize_value(proxy.get('uuid', ''))

        if not server or not port:
            return ""  # 无效节点

        return f"{server}:{port}:{servername}:{password}"

    @staticmethod
    def _get_proxy_key(proxy: Dict[str, Any]) -> str:
        """
        生成代理节点的唯一标识键

        基于以下字段生成唯一键：
        - 基础字段：type, server, port
        - 认证字段：password, uuid, username
        - 连接字段：servername, sni, host
        - 协议特定字段：根据不同协议类型添加关键参数
        """
        # 基础必需字段
        proxy_type = ProxyDeduplicator._normalize_value(proxy.get('type', ''))
        server = ProxyDeduplicator._normalize_value(proxy.get('server', ''))
        port = ProxyDeduplicator._normalize_value(proxy.get('port', ''))

        if not server or not port:
            return ""  # 无效节点

        # 认证相关字段
        password = ProxyDeduplicator._normalize_value(proxy.get('password', ''))
        uuid = ProxyDeduplicator._normalize_value(proxy.get('uuid', ''))
        username = ProxyDeduplicator._normalize_value(proxy.get('username', ''))

        # 连接相关字段
        servername = ProxyDeduplicator._normalize_value(proxy.get('servername', ''))
        sni = ProxyDeduplicator._normalize_value(proxy.get('sni', ''))
        host = ProxyDeduplicator._normalize_value(proxy.get('host', ''))

        # 基础键
        base_key = f"{proxy_type}:{server}:{port}"

        # 认证键（优先级：password > uuid > username）
        auth_key = password or uuid or username

        # 连接键（优先级：servername > sni > host）
        conn_key = servername or sni or host

        # 根据协议类型添加特定字段
        protocol_key = ""
        if proxy_type.lower() in ['ss', 'shadowsocks']:
            cipher = ProxyDeduplicator._normalize_value(proxy.get('cipher', ''))
            protocol_key = f"cipher:{cipher}"
        elif proxy_type.lower() in ['vmess']:
            alter_id = ProxyDeduplicator._normalize_value(proxy.get('alterId', proxy.get('alter-id', '')))
            security = ProxyDeduplicator._normalize_value(proxy.get('security', ''))
            protocol_key = f"alterId:{alter_id}:security:{security}"
        elif proxy_type.lower() in ['vless']:
            flow = ProxyDeduplicator._normalize_value(proxy.get('flow', ''))
            protocol_key = f"flow:{flow}"
        elif proxy_type.lower() in ['trojan']:
            skip_cert_verify = ProxyDeduplicator._normalize_value(proxy.get('skip-cert-verify', ''))
            protocol_key = f"skip-cert:{skip_cert_verify}"
        elif proxy_type.lower() in ['hysteria2', 'hy2']:
            obfs = ProxyDeduplicator._normalize_value(proxy.get('obfs', ''))
            obfs_password = ProxyDeduplicator._normalize_value(proxy.get('obfs-password', ''))
            protocol_key = f"obfs:{obfs}:obfs-pwd:{obfs_password}"
        elif proxy_type.lower() in ['ssr']:
            protocol = ProxyDeduplicator._normalize_value(proxy.get('protocol', ''))
            obfs = ProxyDeduplicator._normalize_value(proxy.get('obfs', ''))
            protocol_key = f"protocol:{protocol}:obfs:{obfs}"

        # 组合最终键
        final_key = f"{base_key}:auth:{auth_key}:conn:{conn_key}:proto:{protocol_key}"
        return final_key

    @staticmethod
    def deduplicate_proxies_simple(proxies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        简化去重方法 - 基于Go代码的快速去重逻辑
        仅基于核心字段进行去重，速度更快但精度稍低
        """
        seen_keys = set()
        result = []
        duplicate_count = 0

        for proxy in proxies:
            # 生成简化唯一键
            key = ProxyDeduplicator._get_simple_proxy_key(proxy)

            # 跳过无效节点
            if not key:
                continue

            # 如果键未见过，则添加到结果中
            if key not in seen_keys:
                seen_keys.add(key)
                result.append(proxy)
            else:
                duplicate_count += 1

        if duplicate_count > 0:
            print(f"简化去重: 发现并移除 {duplicate_count} 个重复节点")

        return result

    @staticmethod
    def deduplicate_proxies(proxies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去除重复的代理节点

        Args:
            proxies: 代理节点列表，每个节点是一个字典

        Returns:
            去重后的代理节点列表
        """
        seen_keys = set()
        result = []
        duplicate_count = 0

        for proxy in proxies:
            # 生成唯一键
            key = ProxyDeduplicator._get_proxy_key(proxy)

            # 跳过无效节点
            if not key:
                continue

            # 如果键未见过，则添加到结果中
            if key not in seen_keys:
                seen_keys.add(key)
                result.append(proxy)
            else:
                duplicate_count += 1

        if duplicate_count > 0:
            print(f"发现并移除 {duplicate_count} 个重复节点")

        return result


class ProxyProcessor:
    """代理节点处理器 - 整合去重和重命名功能，基于Go代码增强"""

    def __init__(self):
        self.deduplicator = ProxyDeduplicator()
        self.renamer = ProxyRenamer()
        self.normalizer = ProxyFieldNormalizer()

    def process_proxies(self, proxies: List[Dict[str, Any]],
                       enable_dedup: bool = True,
                       enable_rename: bool = True,
                       enable_normalize: bool = True,
                       use_simple_dedup: bool = False) -> List[Dict[str, Any]]:
        """
        处理代理节点列表

        Args:
            proxies: 代理节点列表
            enable_dedup: 是否启用去重
            enable_rename: 是否启用重命名
            enable_normalize: 是否启用字段标准化
            use_simple_dedup: 是否使用简化去重（更快但精度稍低）

        Returns:
            处理后的代理节点列表
        """
        result = proxies.copy()

        # 字段标准化处理
        if enable_normalize:
            for i, proxy in enumerate(result):
                result[i] = self.normalizer.normalize_proxy_fields(proxy)
            print("字段标准化完成")

        # 去重处理
        if enable_dedup:
            if use_simple_dedup:
                result = self.deduplicator.deduplicate_proxies_simple(result)
            else:
                result = self.deduplicator.deduplicate_proxies(result)
            print(f"去重完成: {len(proxies)} -> {len(result)}")

        # 重命名处理
        if enable_rename:
            self.renamer.reset_counter()  # 重置计数器
            for proxy in result:
                original_name = proxy.get('name', '')
                new_name = self.renamer.rename(original_name)
                proxy['name'] = new_name
            print("重命名完成")

        return result

    def process_proxy_urls(self, proxy_urls: List[str],
                          enable_dedup: bool = True,
                          enable_rename: bool = True,
                          enable_normalize: bool = True,
                          use_simple_dedup: bool = False) -> List[Dict[str, Any]]:
        """
        处理代理URL列表

        Args:
            proxy_urls: 代理URL列表
            enable_dedup: 是否启用去重
            enable_rename: 是否启用重命名

        Returns:
            处理后的代理节点列表
        """
        # 解析代理URL
        proxies = []
        failed_count = 0

        for url in proxy_urls:
            if not url or not isinstance(url, str):
                continue

            url = url.strip()
            if not url:
                continue

            parsed_proxy = ProxyParser.parse_proxy_url(url)
            if parsed_proxy:
                proxies.append(parsed_proxy)
            else:
                failed_count += 1

        if failed_count > 0:
            print(f"解析失败的URL数量: {failed_count}")

        print(f"成功解析代理节点: {len(proxies)}")

        # 处理解析后的代理节点
        return self.process_proxies(proxies, enable_dedup, enable_rename, enable_normalize, use_simple_dedup)

    def process_mixed_input(self, mixed_data: List[Any],
                           enable_dedup: bool = True,
                           enable_rename: bool = True,
                           enable_normalize: bool = True,
                           use_simple_dedup: bool = False) -> List[Dict[str, Any]]:
        """
        处理混合输入（既包含代理字典也包含代理URL）

        Args:
            mixed_data: 混合数据列表
            enable_dedup: 是否启用去重
            enable_rename: 是否启用重命名

        Returns:
            处理后的代理节点列表
        """
        proxies = []
        failed_count = 0

        for item in mixed_data:
            if isinstance(item, dict):
                # 已经是代理字典格式
                proxies.append(item)
            elif isinstance(item, str):
                # 是代理URL，需要解析
                item = item.strip()
                if not item:
                    continue

                parsed_proxy = ProxyParser.parse_proxy_url(item)
                if parsed_proxy:
                    proxies.append(parsed_proxy)
                else:
                    failed_count += 1
            else:
                failed_count += 1

        if failed_count > 0:
            print(f"处理失败的项目数量: {failed_count}")

        print(f"总共处理代理节点: {len(proxies)}")

        # 处理代理节点
        return self.process_proxies(proxies, enable_dedup, enable_rename, enable_normalize, use_simple_dedup)

    def get_statistics(self) -> Dict[str, int]:
        """获取各地区节点统计信息"""
        stats = {}
        for region in self.renamer.region_patterns:
            count = self.renamer.counter.get_count(region['key'])
            if count > 0:
                stats[f"{region['flag']}{region['name']}"] = count

        other_count = self.renamer.counter.get_count('other')
        if other_count > 0:
            stats["🌀其他"] = other_count

        return stats


def main():
    """示例用法"""
    print("=== 代理节点去重和重命名工具演示 ===\n")

    # 示例1: 处理代理字典
    print("1. 处理代理字典格式:")
    sample_proxies = [
        {
            'name': 'HK-Server-01',
            'type': 'ss',
            'server': '*******',
            'port': 8080,
            'password': 'password123',
            'servername': 'hk.example.com'
        },
        {
            'name': 'HK-Server-01',  # 重复节点
            'type': 'ss',
            'server': '*******',
            'port': 8080,
            'password': 'password123',
            'servername': 'hk.example.com'
        },
        {
            'name': 'US-Server-01',
            'type': 'vmess',
            'server': '*******',
            'port': 443,
            'uuid': 'uuid123',
            'servername': 'us.example.com'
        },
        {
            'name': 'SG-Node-Fast',
            'type': 'trojan',
            'server': '**********',
            'port': 443,
            'password': 'trojan123'
        }
    ]

    # 创建处理器
    processor = ProxyProcessor()

    print("原始节点:")
    for i, proxy in enumerate(sample_proxies, 1):
        print(f"{i}. {proxy['name']} - {proxy['server']}:{proxy['port']}")

    print(f"\n处理前节点数量: {len(sample_proxies)}")

    # 处理节点
    processed_proxies = processor.process_proxies(sample_proxies)

    print(f"处理后节点数量: {len(processed_proxies)}")
    print("\n处理后节点:")
    for i, proxy in enumerate(processed_proxies, 1):
        print(f"{i}. {proxy['name']} - {proxy['server']}:{proxy['port']}")

    # 显示统计信息
    stats = processor.get_statistics()
    print(f"\n地区统计:")
    for region, count in stats.items():
        print(f"{region}: {count}")

    print("\n" + "="*50 + "\n")

    # 示例2: 处理代理URL
    print("2. 处理代理URL格式:")
    sample_urls = [
        # Shadowsocks示例
        "ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ=@*******:8080#HK-SS-Node",
        # VMess示例（简化版，实际需要完整的base64编码JSON）
        # "vmess://eyJ2IjoiMiIsInBzIjoiVVMtVk1lc3MiLCJhZGQiOiI1LjYuNy44IiwicG9ydCI6IjQ0MyIsImlkIjoidXVpZDEyMyIsImFpZCI6IjAiLCJzY3kiOiJhdXRvIiwibmV0IjoidGNwIiwidHlwZSI6Im5vbmUiLCJob3N0IjoiIiwicGF0aCI6IiIsInRscyI6InRscyIsInNuaSI6IiJ9",
        # Trojan示例
        "trojan://password123@**********:443?allowInsecure=1&sni=sg.example.com#SG-Trojan-Node",
    ]

    print("代理URL列表:")
    for i, url in enumerate(sample_urls, 1):
        print(f"{i}. {url[:60]}...")

    print(f"\n处理前URL数量: {len(sample_urls)}")

    # 重置处理器计数器
    processor.renamer.reset_counter()

    # 处理URL
    processed_from_urls = processor.process_proxy_urls(sample_urls)

    print(f"处理后节点数量: {len(processed_from_urls)}")
    print("\n处理后节点:")
    for i, proxy in enumerate(processed_from_urls, 1):
        print(f"{i}. {proxy['name']} - {proxy['server']}:{proxy['port']}")

    print("\n" + "="*50 + "\n")

    # 示例3: 处理混合输入
    print("3. 处理混合输入（字典 + URL）:")
    mixed_data = [
        # 代理字典
        {
            'name': 'JP-Direct',
            'type': 'ss',
            'server': '***********',
            'port': 8080,
            'password': 'jp-password',
            'cipher': 'aes-256-gcm'
        },
        # 代理URL
        "ss://YWVzLTI1Ni1nY206a3ItcGFzc3dvcmQ=@***********:8080#KR-SS-Node",
    ]

    print("混合数据:")
    for i, item in enumerate(mixed_data, 1):
        if isinstance(item, dict):
            print(f"{i}. [字典] {item['name']} - {item['server']}:{item['port']}")
        else:
            print(f"{i}. [URL] {item[:60]}...")

    print(f"\n处理前数据数量: {len(mixed_data)}")

    # 重置处理器计数器
    processor.renamer.reset_counter()

    # 处理混合数据
    processed_mixed = processor.process_mixed_input(mixed_data)

    print(f"处理后节点数量: {len(processed_mixed)}")
    print("\n处理后节点:")
    for i, proxy in enumerate(processed_mixed, 1):
        print(f"{i}. {proxy['name']} - {proxy['server']}:{proxy['port']}")

    print("\n" + "="*50 + "\n")

    # 示例4: 展示Base64工具
    print("4. Base64工具演示:")
    test_strings = [
        "aGVsbG8gd29ybGQ=",  # "hello world" 的base64
        "not_base64_string",
        "YWVzLTI1Ni1nY206cGFzc3dvcmQ=",  # "aes-256-gcm:password" 的base64
        "normal text"
    ]

    for test_str in test_strings:
        is_b64 = Base64Utils.is_base64_string(test_str)
        decoded = Base64Utils.decode_base64(test_str)
        print(f"'{test_str}' -> Base64: {is_b64}, 解码: '{decoded}'")

    print("\n" + "="*50 + "\n")

    # 示例5: 展示增强功能
    print("5. 增强功能演示（基于Go代码整合）:")

    # 字段标准化演示
    print("字段标准化演示:")
    test_proxy = {
        'name': 'Test-Node',
        'type': 'hysteria2',
        'server': '*******',
        'port': 443,
        'obfs_password': 'test123',  # 下划线格式
        'skip_cert_verify': True     # 下划线格式
    }

    print(f"标准化前: {test_proxy}")
    normalized = ProxyFieldNormalizer.normalize_proxy_fields(test_proxy)
    print(f"标准化后: {normalized}")

    # 简化去重演示
    print(f"\n简化去重演示:")
    duplicate_proxies = [
        {'name': 'Node1', 'type': 'ss', 'server': '*******', 'port': 8080, 'password': 'pass1'},
        {'name': 'Node2', 'type': 'ss', 'server': '*******', 'port': 8080, 'password': 'pass1'},  # 重复
        {'name': 'Node3', 'type': 'ss', 'server': '*******', 'port': 8080, 'password': 'pass2'},
    ]

    print(f"原始节点数: {len(duplicate_proxies)}")

    # 使用简化去重
    processor_simple = ProxyProcessor()
    result_simple = processor_simple.process_proxies(
        duplicate_proxies.copy(),
        enable_dedup=True,
        enable_rename=False,
        enable_normalize=False,
        use_simple_dedup=True
    )
    print(f"简化去重后: {len(result_simple)}")

    # 使用完整去重
    processor_full = ProxyProcessor()
    result_full = processor_full.process_proxies(
        duplicate_proxies.copy(),
        enable_dedup=True,
        enable_rename=False,
        enable_normalize=False,
        use_simple_dedup=False
    )
    print(f"完整去重后: {len(result_full)}")

    print("\n演示完成！")
    print("\n" + "="*50 + "\n")

    # 示例6: 新增国家和其他地区重命名演示
    print("6. 新增国家支持和其他地区重命名优化:")

    # 测试新增国家
    test_names = [
        "马来西亚_优刻得专线_多莉的猫耳",
        "泰国节点_BGP高级中继",
        "越南_VIP专线",
        "其他地区_BGP高级中继_琳妮特的屁股",
        "未知地区_测试节点_随机名称"
    ]

    print("新增国家和其他地区重命名测试:")
    renamer_test = ProxyRenamer()
    for name in test_names:
        renamed = renamer_test.rename(name)
        print(f"  原名: {name}")
        print(f"  重命名: {renamed}")
        print()

    print("\n🎉 优化功能说明:")
    print("1. ✅ 字段标准化 - 统一字段命名格式（下划线转横杠）")
    print("2. ✅ 增强解析器 - 支持更多传输层配置（WebSocket、gRPC、Reality等）")
    print("3. ✅ 简化去重选项 - 提供快速去重模式")
    print("4. ✅ 扩展地区支持 - 新增24个国家/地区（马来西亚、泰国、越南等）")
    print("5. ✅ 其他地区重命名优化 - 完全替换为简洁格式（如🌀其他001）")
    print("6. ✅ 兼容性保持 - 保留原始参数以确保向后兼容")

    print(f"\n📊 当前支持的国家/地区总数: {len(renamer_test.region_patterns)} 个")
    print("🌍 新增支持的国家/地区:")
    new_regions = [
        "🇲🇾马来西亚", "🇹🇭泰国", "🇻🇳越南", "🇵🇭菲律宾", "🇮🇩印度尼西亚",
        "🇹🇷土耳其", "🇮🇱以色列", "🇿🇦南非", "🇦🇷阿根廷", "🇨🇱智利",
        "🇲🇽墨西哥", "🇳🇴挪威", "🇸🇪瑞典", "🇩🇰丹麦", "🇧🇪比利时",
        "🇦🇹奥地利", "🇨🇿捷克", "🇷🇴罗马尼亚", "🇧🇬保加利亚", "🇬🇷希腊",
        "🇵🇹葡萄牙", "🇪🇸西班牙", "🇮🇪爱尔兰", "🇳🇿新西兰"
    ]
    for i, region in enumerate(new_regions, 1):
        print(f"  {i:2d}. {region}")
        if i % 6 == 0:  # 每6个换行
            print()


def find_yaml_files(directory: str = ".") -> List[str]:
    """
    在指定目录中查找所有YAML文件

    Args:
        directory: 搜索目录，默认为当前目录

    Returns:
        YAML文件路径列表
    """
    yaml_files = []

    # 支持的YAML文件扩展名
    yaml_extensions = ['*.yaml', '*.yml', '*.YAML', '*.YML']

    for extension in yaml_extensions:
        pattern = os.path.join(directory, extension)
        yaml_files.extend(glob.glob(pattern))

    # 去重并排序
    yaml_files = sorted(list(set(yaml_files)))

    return yaml_files


def load_proxies_from_yaml(file_path: str) -> List[Dict[str, Any]]:
    """
    从YAML文件加载代理节点

    Args:
        file_path: YAML文件路径

    Returns:
        代理节点列表
    """
    try:
        import yaml

        # 自定义YAML加载器，支持各种自定义标签
        class CustomYamlLoader(yaml.SafeLoader):
            pass

        def str_constructor(loader, node):
            """处理 !<str> 标签，将其作为普通字符串处理"""
            return loader.construct_scalar(node)

        def generic_constructor(loader, tag_suffix, node):
            """处理所有未知标签，将其作为普通值处理"""
            # tag_suffix 参数用于PyYAML内部，这里不直接使用但需要保留
            if isinstance(node, yaml.ScalarNode):
                return loader.construct_scalar(node)
            elif isinstance(node, yaml.SequenceNode):
                return loader.construct_sequence(node)
            elif isinstance(node, yaml.MappingNode):
                return loader.construct_mapping(node)
            else:
                return None

        # 注册自定义标签处理器
        CustomYamlLoader.add_constructor('tag:yaml.org,2002:str', str_constructor)
        # 注册通用构造器处理所有未知标签
        CustomYamlLoader.add_multi_constructor('', generic_constructor)

        print(f"正在读取文件: {file_path}")
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.load(f, Loader=CustomYamlLoader)
            if data and 'proxies' in data:
                return data.get('proxies', [])
            else:
                print(f"警告: 文件 {file_path} 中未找到 'proxies' 字段")
                return []
    except ImportError:
        print("警告: 需要安装PyYAML库来支持YAML文件读取")
        print("安装命令: pip install PyYAML")
        return []
    except Exception as e:
        print(f"读取YAML文件失败: {e}")
        return []


def load_proxies_from_multiple_yaml(file_paths: List[str]) -> List[Dict[str, Any]]:
    """
    从多个YAML文件加载代理节点

    Args:
        file_paths: YAML文件路径列表

    Returns:
        合并后的代理节点列表
    """
    all_proxies = []

    for file_path in file_paths:
        print(f"正在读取文件: {file_path}")
        proxies = load_proxies_from_yaml(file_path)
        if proxies:
            print(f"  - 找到 {len(proxies)} 个代理节点")
            all_proxies.extend(proxies)
        else:
            print(f"  - 未找到代理节点或文件读取失败")

    return all_proxies


def auto_process_yaml_files(directory: str = ".",
                           output_file: str = None,
                           enable_dedup: bool = True,
                           enable_rename: bool = True,
                           stats_only: bool = False) -> List[Dict[str, Any]]:
    """
    自动搜索并处理目录中的所有YAML文件

    Args:
        directory: 搜索目录，默认为当前目录
        output_file: 输出文件路径，如果为None则自动生成
        enable_dedup: 是否启用去重
        enable_rename: 是否启用重命名
        stats_only: 是否仅显示统计信息，不保存文件

    Returns:
        处理后的代理节点列表
    """
    print(f"🔍 正在搜索目录中的YAML文件: {os.path.abspath(directory)}")

    # 查找YAML文件
    yaml_files = find_yaml_files(directory)

    if not yaml_files:
        print("❌ 未找到任何YAML文件")
        return []

    print(f"📁 找到 {len(yaml_files)} 个YAML文件:")
    for i, file_path in enumerate(yaml_files, 1):
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        print(f"  {i}. {file_path} ({file_size} bytes)")

    # 加载所有代理节点
    print(f"\n📖 正在读取YAML文件...")
    all_proxies = load_proxies_from_multiple_yaml(yaml_files)

    if not all_proxies:
        print("❌ 所有文件中都未找到有效的代理节点")
        return []

    print(f"✅ 总共加载了 {len(all_proxies)} 个代理节点")

    # 处理代理节点
    print(f"\n⚙️ 正在处理代理节点...")
    processor = ProxyProcessor()
    processed_proxies = processor.process_proxies(all_proxies, enable_dedup, enable_rename)

    # 显示统计信息
    stats = processor.get_statistics()
    if stats:
        print(f"\n📊 地区统计:")
        for region, count in stats.items():
            print(f"  {region}: {count}")

    # 保存结果
    if not stats_only:
        if output_file is None:
            # 自动生成输出文件名
            output_file = "processed_proxies.yaml"

        save_proxies_to_yaml(processed_proxies, output_file)
        print(f"\n🎉 处理完成！输出文件: {output_file}")
    else:
        print(f"\n✅ 统计完成！（仅统计模式，未保存文件）")

    return processed_proxies


def save_proxies_to_yaml(proxies: List[Dict[str, Any]], file_path: str):
    """
    将代理节点保存到YAML文件

    Args:
        proxies: 代理节点列表
        file_path: 输出文件路径
    """
    try:
        import yaml

        # 自定义YAML格式化器，确保正确的缩进
        class ProxyYamlDumper(yaml.SafeDumper):
            def increase_indent(self, flow=False, indentless=False):
                # indentless 参数用于PyYAML内部控制缩进，这里强制使用缩进
                return super(ProxyYamlDumper, self).increase_indent(flow, False)

        data = {'proxies': proxies}

        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(
                data,
                f,
                Dumper=ProxyYamlDumper,
                default_flow_style=False,
                allow_unicode=True,
                indent=2,
                width=1000,
                sort_keys=False
            )
        print(f"代理节点已保存到: {file_path}")
    except ImportError:
        print("警告: 需要安装PyYAML库来支持YAML文件写入")
        print("安装命令: pip install PyYAML")
    except Exception as e:
        print(f"保存YAML文件失败: {e}")


def cli_interface():
    """命令行界面"""
    import argparse

    parser = argparse.ArgumentParser(description='代理节点去重和重命名工具')
    parser.add_argument('input_file', nargs='?', help='输入的YAML文件路径（可选，不指定时自动搜索当前目录）')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-d', '--directory', default='.', help='搜索YAML文件的目录（默认为当前目录）')
    parser.add_argument('--no-dedup', action='store_true', help='禁用去重功能')
    parser.add_argument('--no-rename', action='store_true', help='禁用重命名功能')
    parser.add_argument('--stats-only', action='store_true', help='仅显示统计信息，不保存文件')
    parser.add_argument('--auto', action='store_true', help='强制使用自动搜索模式，即使指定了输入文件')

    args = parser.parse_args()

    if args.input_file and not args.auto:
        # 处理单个文件
        print(f"📄 处理单个文件: {args.input_file}")
        proxies = load_proxies_from_yaml(args.input_file)
        if not proxies:
            print("❌ 未找到有效的代理节点")
            return

        processor = ProxyProcessor()
        processed_proxies = processor.process_proxies(
            proxies,
            enable_dedup=not args.no_dedup,
            enable_rename=not args.no_rename
        )

        # 显示统计信息
        stats = processor.get_statistics()
        if stats:
            print(f"\n📊 地区统计:")
            for region, count in stats.items():
                print(f"  {region}: {count}")

        # 保存结果
        if not args.stats_only:
            output_file = args.output or args.input_file.replace('.yaml', '_processed.yaml').replace('.yml', '_processed.yml')
            save_proxies_to_yaml(processed_proxies, output_file)
    else:
        # 自动搜索模式
        if args.input_file and args.auto:
            print(f"🔄 启用自动搜索模式（忽略输入文件: {args.input_file}）")
        else:
            print(f"🔍 未指定输入文件，启用自动搜索模式")

        # 自动搜索并处理
        processed_proxies = auto_process_yaml_files(
            directory=args.directory,
            output_file=args.output,
            enable_dedup=not args.no_dedup,
            enable_rename=not args.no_rename,
            stats_only=args.stats_only
        )


if __name__ == '__main__':
    cli_interface()
